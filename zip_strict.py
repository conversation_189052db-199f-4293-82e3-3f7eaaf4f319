#!/usr/bin/env python3
"""
🗜️ Script STRICT pour Zipper PestAlert - Limite 3MB par fichier
==============================================================

Ce script crée une archive ZIP en excluant:
- Tous les dossiers node_modules, dist, .git, etc.
- Tous les fichiers > 3MB
- Tous les dossiers de cache

Usage: python zip_strict.py
"""

import os
import zipfile
from datetime import datetime
from pathlib import Path

# Limite de taille par fichier (3MB)
MAX_FILE_SIZE = 3 * 1024 * 1024  # 3MB en bytes

def get_dir_size(dir_path):
    """Calcule la taille totale d'un dossier"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(dir_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except:
                    pass
    except:
        pass
    return total_size

def should_exclude_directory(dir_path):
    """Détermine si un dossier doit être exclu"""
    dir_name = dir_path.name.lower()
    
    # Dossiers toujours exclus
    excluded_dirs = {
        'node_modules',
        'dist',
        'build',
        '.git',
        '.vscode',
        '.idea',
        'logs',
        'sessions',
        'coverage',
        '.cache',
        '.tmp',
        'tmp',
        'temp',
        '.wwebjs_cache',
        '.wwebjs_auth',
        '.next',
        '.nuxt'
    }
    
    if dir_name in excluded_dirs:
        return True, f"Dossier exclu: {dir_name}"
    
    # Vérifier la taille du dossier
    try:
        dir_size = get_dir_size(dir_path)
        if dir_size > MAX_FILE_SIZE:
            return True, f"Dossier trop volumineux: {format_size(dir_size)}"
    except:
        pass
    
    return False, ""

def should_exclude_file(file_path):
    """Détermine si un fichier doit être exclu"""
    file_name = file_path.name.lower()
    
    # Extensions toujours exclues
    excluded_extensions = {
        '.log',
        '.tmp',
        '.cache',
        '.html',  # Cache WhatsApp
        '.mp4',
        '.avi',
        '.mov',
        '.zip',
        '.rar',
        '.7z'
    }
    
    # Fichiers système exclus
    excluded_files = {
        '.ds_store',
        'thumbs.db',
        'desktop.ini'
    }
    
    # Vérifier les extensions
    file_ext = Path(file_name).suffix.lower()
    if file_ext in excluded_extensions:
        return True, f"Extension exclue: {file_ext}"
    
    # Vérifier les noms de fichiers
    if file_name in excluded_files:
        return True, f"Fichier système: {file_name}"
    
    # Vérifier la taille du fichier
    try:
        file_size = file_path.stat().st_size
        if file_size > MAX_FILE_SIZE:
            return True, f"Fichier trop volumineux: {format_size(file_size)}"
    except:
        return True, "Erreur de lecture"
    
    return False, ""

def format_size(size_bytes):
    """Formate la taille en unités lisibles"""
    if size_bytes == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB']
    unit_index = 0
    size = float(size_bytes)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.1f} {units[unit_index]}"

def create_strict_archive():
    """Crée l'archive ZIP avec des règles strictes"""
    # Nom du fichier avec timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_filename = f'pestalert-strict-{timestamp}.zip'
    
    print("🗜️  Création d'archive STRICTE PestAlert")
    print("="*60)
    print(f"📦 Archive: {zip_filename}")
    print(f"📏 Limite par fichier: {format_size(MAX_FILE_SIZE)}")
    print("❌ EXCLUSIONS:")
    print("   - node_modules, dist, .git, logs, sessions")
    print("   - Fichiers > 3MB")
    print("   - Dossiers volumineux")
    print("   - Cache WhatsApp (.wwebjs_cache)")
    print("="*60)
    print()
    
    stats = {
        'files_added': 0,
        'files_excluded': 0,
        'dirs_excluded': 0,
        'total_size': 0,
        'excluded_size': 0
    }
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
            
            for root, dirs, files in os.walk('.'):
                root_path = Path(root)
                
                # Filtrer les dossiers
                original_dirs = dirs[:]
                dirs[:] = []
                
                for d in original_dirs:
                    dir_path = root_path / d
                    should_exclude, reason = should_exclude_directory(dir_path)
                    
                    if should_exclude:
                        stats['dirs_excluded'] += 1
                        print(f"📁❌ {dir_path} - {reason}")
                    else:
                        dirs.append(d)
                
                # Traiter les fichiers
                for file in files:
                    file_path = root_path / file
                    
                    should_exclude, reason = should_exclude_file(file_path)
                    
                    if should_exclude:
                        stats['files_excluded'] += 1
                        try:
                            file_size = file_path.stat().st_size
                            stats['excluded_size'] += file_size
                            if file_size > 1024 * 1024:  # Afficher seulement les gros fichiers exclus
                                print(f"📄❌ {file_path} - {reason}")
                        except:
                            print(f"📄❌ {file_path} - {reason}")
                    else:
                        try:
                            # Ajouter le fichier
                            file_size = file_path.stat().st_size
                            stats['total_size'] += file_size
                            
                            arcname = str(file_path).lstrip('./')
                            arcname = arcname.replace('\\', '/')
                            zipf.write(file_path, arcname)
                            
                            stats['files_added'] += 1
                            
                            # Afficher seulement les fichiers importants
                            if any(ext in str(file_path).lower() for ext in ['.env', '.md', '.json', '.py', '.js', '.ts']) or file_size > 100000:
                                print(f"📄✅ {file_path} ({format_size(file_size)})")
                            
                        except Exception as e:
                            stats['files_excluded'] += 1
                            print(f"📄⚠️  Erreur {file_path}: {e}")
        
        # Statistiques finales
        archive_size = os.path.getsize(zip_filename)
        
        print("\n" + "="*70)
        print("📊 RÉSUMÉ STRICT")
        print("="*70)
        print(f"📦 Archive: {zip_filename}")
        print(f"✅ Fichiers inclus: {stats['files_added']:,}")
        print(f"❌ Fichiers exclus: {stats['files_excluded']:,}")
        print(f"📁 Dossiers exclus: {stats['dirs_excluded']:,}")
        print(f"📏 Taille incluse: {format_size(stats['total_size'])}")
        print(f"🗑️  Taille exclue: {format_size(stats['excluded_size'])}")
        print(f"🗜️  Archive finale: {format_size(archive_size)}")
        
        if stats['total_size'] > 0:
            compression = (1 - archive_size / stats['total_size']) * 100
            print(f"📉 Compression: {compression:.1f}%")
        
        print("\n🎉 Archive STRICTE créée avec succès!")
        
        if archive_size > 50 * 1024 * 1024:  # Plus de 50MB
            print(f"\n⚠️  ATTENTION: Archive encore volumineuse ({format_size(archive_size)})")
            print("   Vérifiez s'il y a encore des gros fichiers inclus.")
        else:
            print(f"\n✅ Taille acceptable: {format_size(archive_size)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        return False

def main():
    """Fonction principale"""
    print("🌾 PestAlert - Archive STRICTE (Max 3MB par fichier)")
    print()
    
    # Vérifier qu'on est dans le bon dossier
    if not os.path.exists('package.json'):
        print("⚠️  ATTENTION: package.json non trouvé!")
        print("   Assurez-vous d'être dans le dossier racine de pestalert-bot")
        print()
        response = input("Continuer quand même? (y/N): ")
        if response.lower() != 'y':
            print("❌ Opération annulée.")
            return
    
    # Créer l'archive
    success = create_strict_archive()
    
    if success:
        print("\n🚀 Archive créée avec des règles STRICTES!")
        print("   Tous les fichiers > 3MB ont été exclus.")
        print("   L'archive devrait être beaucoup plus petite maintenant.")
    
    input("\nAppuyez sur Entrée pour fermer...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Opération annulée par l'utilisateur.")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        input("Appuyez sur Entrée pour fermer...")
