#!/usr/bin/env python3
"""
🗜️ Script Simple de Création d'Archive PestAlert
===============================================

Script simple pour zipper le projet PestAlert sans node_modules et dist.

Usage: python zip_project.py
"""

import os
import zipfile
from datetime import datetime
from pathlib import Path

def should_exclude(path):
    """Détermine si un fichier ou dossier doit être exclu"""
    path_str = str(path).lower()
    name = path.name.lower()
    
    # Dossiers à exclure
    excluded_dirs = [
        'node_modules',
        'dist',
        'build',
        '.git',
        '.vscode',
        '.idea',
        'logs',
        'sessions',
        'coverage',
        '.cache',
        'tmp',
        'temp'
    ]
    
    # Fichiers à exclure
    excluded_files = [
        '.env',
        '.env.local',
        '.env.production',
        '.env.development',
        '.env_global'
    ]
    
    # Extensions à exclure
    excluded_extensions = [
        '.log',
        '.tmp',
        '.cache'
    ]
    
    # Vérifier les dossiers
    for excluded_dir in excluded_dirs:
        if excluded_dir in path_str:
            return True
    
    # Vérifier les fichiers
    if name in excluded_files:
        return True
    
    # Vérifier les extensions
    for ext in excluded_extensions:
        if name.endswith(ext):
            return True
    
    return False

def create_zip():
    """Crée l'archive ZIP du projet"""
    # Nom du fichier avec timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_filename = f'pestalert-bot-{timestamp}.zip'
    
    print(f"🗜️  Création de l'archive: {zip_filename}")
    print("📁 Exclusion de: node_modules, dist, .git, logs, sessions, .env, etc.")
    print()
    
    files_added = 0
    files_excluded = 0
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk('.'):
            root_path = Path(root)
            
            # Filtrer les dossiers à exclure
            dirs[:] = [d for d in dirs if not should_exclude(root_path / d)]
            
            for file in files:
                file_path = root_path / file
                
                if should_exclude(file_path):
                    files_excluded += 1
                    print(f"⏭️  Exclusion: {file_path}")
                else:
                    # Ajouter le fichier à l'archive
                    arcname = str(file_path).lstrip('./')
                    zipf.write(file_path, arcname)
                    files_added += 1
                    print(f"✅ Ajout: {file_path}")
    
    # Statistiques
    zip_size = os.path.getsize(zip_filename)
    zip_size_mb = zip_size / (1024 * 1024)
    
    print("\n" + "="*50)
    print("📊 RÉSUMÉ")
    print("="*50)
    print(f"📦 Archive créée: {zip_filename}")
    print(f"✅ Fichiers inclus: {files_added}")
    print(f"⏭️  Fichiers exclus: {files_excluded}")
    print(f"📏 Taille de l'archive: {zip_size_mb:.2f} MB")
    print("\n🎉 Archive créée avec succès!")

if __name__ == '__main__':
    try:
        create_zip()
    except KeyboardInterrupt:
        print("\n❌ Opération annulée par l'utilisateur.")
    except Exception as e:
        print(f"❌ Erreur: {e}")
