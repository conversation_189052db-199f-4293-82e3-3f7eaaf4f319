{"name": "@pestalert/dashboard", "version": "1.0.0", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.0.0", "@tanstack/react-query": "^4.0.0", "react-leaflet": "^4.2.0", "leaflet": "^1.9.0", "chart.js": "^4.0.0", "react-chartjs-2": "^5.0.0", "recharts": "^2.8.0", "@tanstack/react-table": "^8.0.0", "react-hook-form": "^7.45.0", "tailwindcss": "^3.3.0", "lucide-react": "^0.263.0", "socket.io-client": "^4.7.0", "date-fns": "^2.30.0", "axios": "^1.4.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/leaflet": "^1.9.0", "@vitejs/plugin-react": "^4.0.0", "vite": "^4.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0"}}