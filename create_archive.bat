@echo off
echo ========================================
echo    🗜️ PestAlert - Création d'Archive
echo ========================================
echo.
echo Ce script va créer une archive ZIP du projet
echo en excluant les dossiers volumineux comme
echo node_modules, dist, logs, sessions, etc.
echo.

:: Vérifier si Python est installé
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERREUR: Python n'est pas installé ou pas dans le PATH!
    echo.
    echo Veuillez installer Python depuis: https://python.org
    echo Ou utilisez la version manuelle ci-dessous.
    echo.
    goto :manual_zip
)

echo ✅ Python détecté
echo.

:: Vérifier si le script Python existe
if not exist "zip_project.py" (
    echo ❌ ERREUR: Le script zip_project.py n'existe pas!
    echo.
    goto :manual_zip
)

echo 🚀 Lancement de l'archivage avec Python...
echo.
python zip_project.py

if %errorlevel% equ 0 (
    echo.
    echo ✅ Archive créée avec succès!
    goto :end
) else (
    echo.
    echo ❌ Erreur lors de la création de l'archive avec Python.
    echo Tentative avec la méthode manuelle...
    goto :manual_zip
)

:manual_zip
echo.
echo ========================================
echo    📦 Méthode Alternative (PowerShell)
echo ========================================
echo.

:: Créer un nom de fichier avec timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"
set "zipfile=pestalert-bot-%timestamp%.zip"

echo 📦 Création de l'archive: %zipfile%
echo.

:: Utiliser PowerShell pour créer l'archive
powershell -Command "& { ^
    $source = Get-Location; ^
    $destination = '%zipfile%'; ^
    $exclude = @('node_modules', 'dist', 'build', '.git', '.vscode', 'logs', 'sessions', 'coverage', 'tmp', 'temp'); ^
    $files = Get-ChildItem -Path $source -Recurse | Where-Object { ^
        $exclude_this = $false; ^
        foreach ($ex in $exclude) { ^
            if ($_.FullName -like \"*$ex*\") { $exclude_this = $true; break } ^
        } ^
        -not $exclude_this -and -not $_.PSIsContainer ^
    }; ^
    Compress-Archive -Path $files -DestinationPath $destination -Force; ^
    Write-Host \"Archive créée: $destination\" ^
}"

if %errorlevel% equ 0 (
    echo.
    echo ✅ Archive créée avec succès avec PowerShell!
) else (
    echo.
    echo ❌ Erreur avec PowerShell. Création manuelle nécessaire.
    echo.
    echo 📝 Instructions manuelles:
    echo 1. Sélectionnez tous les fichiers SAUF:
    echo    - node_modules/
    echo    - dist/
    echo    - .git/
    echo    - logs/
    echo    - sessions/
    echo    - fichiers .env
    echo 2. Clic droit > Envoyer vers > Dossier compressé
    echo 3. Renommez en pestalert-bot-backup.zip
)

:end
echo.
echo ========================================
echo 📋 Contenu typique de l'archive:
echo ========================================
echo ✅ Code source (src/, packages/, apps/)
echo ✅ Configuration (package.json, tsconfig.json)
echo ✅ Documentation (README.md, docs/)
echo ✅ Scripts (*.bat, *.js)
echo ❌ node_modules/ (exclu)
echo ❌ dist/ (exclu)
echo ❌ .git/ (exclu)
echo ❌ logs/ (exclu)
echo ❌ sessions/ (exclu)
echo ❌ fichiers .env (exclu pour sécurité)
echo.
echo 💡 Pour inclure les .env, utilisez:
echo    python create_archive.py --include-env
echo.
pause
