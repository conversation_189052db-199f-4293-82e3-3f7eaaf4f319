# ========================================
# 🌾 PestAlert - Configuration Globale
# ========================================
# 
# Ce fichier contient toutes les variables d'environnement
# nécessaires pour tous les packages du projet PestAlert.
# 
# Instructions:
# 1. Copiez ce fichier vers .env_global
# 2. Configurez les valeurs selon votre environnement
# 3. Chaque package aura son propre .env qui référence ces valeurs
#
# ========================================

# ========================================
# 🗄️ BASE DE DONNÉES
# ========================================

# URL de connexion PostgreSQL
# Développement local:
DATABASE_URL="postgresql://username:password@localhost:5432/pestalert_dev"

# Production (exemple avec Neon):
# DATABASE_URL="postgresql://username:<EMAIL>/pestalert"

# Configuration Prisma
PRISMA_GENERATE_DATAPROXY=false
PRISMA_HIDE_UPDATE_MESSAGE=true

# ========================================
# 🔑 OPENEPI API CONFIGURATION
# ========================================

# URLs de base OpenEPI
OPENEPI_BASE_URL="https://api.openepi.io"
OPENEPI_AUTH_URL="https://auth.openepi.io/realms/openepi/protocol/openid-connect/token"

# Credentials OpenEPI (fournis par OpenEPI)
OPENEPI_CLIENT_ID="aresgn-testpestsAPI"
OPENEPI_CLIENT_SECRET="gHrAAcKkMkvEDfDijdqqBXULbqjGzlyK"

# Configuration des timeouts (en millisecondes)
OPENEPI_TIMEOUT=30000
OPENEPI_RETRY_ATTEMPTS=3
OPENEPI_RETRY_DELAY=1000

# ========================================
# 🔒 SÉCURITÉ ET AUTHENTIFICATION
# ========================================

# JWT Secret (générez une clé forte en production)
JWT_SECRET="your_super_secret_jwt_key_here_change_in_production"
JWT_EXPIRES_IN="24h"
JWT_REFRESH_EXPIRES_IN="7d"

# Bcrypt rounds pour le hashage des mots de passe
BCRYPT_ROUNDS=12

# Session secret pour Express
SESSION_SECRET="your_session_secret_here"

# ========================================
# 📱 WHATSAPP CONFIGURATION
# ========================================

# Chemin de stockage des sessions WhatsApp
WHATSAPP_SESSION_PATH="./sessions"
WHATSAPP_SESSION_SECRET="your_whatsapp_session_secret"

# Configuration WhatsApp Web.js
WHATSAPP_PUPPETEER_ARGS="--no-sandbox,--disable-setuid-sandbox"
WHATSAPP_HEADLESS=true
WHATSAPP_DEVTOOLS=false

# Numéros autorisés (optionnel, séparer par des virgules)
# WHATSAPP_ALLOWED_NUMBERS="+33123456789,+33987654321"

# ========================================
# 🌐 SERVEURS ET PORTS
# ========================================

# API Backend
API_PORT=3001
API_HOST="localhost"
API_BASE_URL="http://localhost:3001"

# Dashboard
DASHBOARD_PORT=5173
DASHBOARD_HOST="localhost"
DASHBOARD_BASE_URL="http://localhost:5173"

# Site Web Public
WEB_PORT=5174
WEB_HOST="localhost"
WEB_BASE_URL="http://localhost:5174"

# WebSocket pour temps réel
SOCKET_PORT=3001
SOCKET_URL="http://localhost:3001"

# ========================================
# 🗄️ CACHE ET REDIS (OPTIONNEL)
# ========================================

# Redis pour le cache et les sessions
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""
REDIS_DB=0

# Configuration du cache
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# ========================================
# 📧 NOTIFICATIONS ET ALERTES
# ========================================

# Email SMTP (pour les notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Webhook pour notifications d'urgence
ALERT_NOTIFICATION_WEBHOOK="https://your-webhook-url.com/alerts"

# Numéros d'urgence
EMERGENCY_PHONE="+33123456789"
EMERGENCY_EMAIL="<EMAIL>"

# ========================================
# 🗺️ CARTES ET GÉOLOCALISATION
# ========================================

# Clé API pour les cartes (OpenStreetMap, Google Maps, etc.)
MAP_API_KEY="your_map_api_key_here"
MAP_DEFAULT_CENTER_LAT=6.1319
MAP_DEFAULT_CENTER_LNG=1.2228
MAP_DEFAULT_ZOOM=8

# ========================================
# 📊 LOGGING ET MONITORING
# ========================================

# Niveau de logging
LOG_LEVEL="info"
LOG_FORMAT="combined"
LOG_RETENTION_DAYS=90

# Métriques et monitoring
METRICS_COLLECTION_INTERVAL=300000
METRICS_RETENTION_DAYS=90
ENABLE_METRICS=true

# ========================================
# 🔧 ENVIRONNEMENT ET DEBUG
# ========================================

# Environnement de déploiement
NODE_ENV="development"

# Debug mode
DEBUG=true
DEBUG_NAMESPACE="pestalert:*"

# Verbose logging
VERBOSE=false

# ========================================
# 📱 FRONTEND CONFIGURATION (VITE)
# ========================================

# URLs pour les applications frontend
VITE_API_URL="http://localhost:3001"
VITE_SOCKET_URL="http://localhost:3001"
VITE_MAP_API_KEY="your_map_api_key_here"

# Configuration de l'application
VITE_APP_NAME="PestAlert"
VITE_APP_VERSION="1.0.0"
VITE_APP_ENV="development"

# Features flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_SENTRY=false
VITE_ENABLE_HOTJAR=false

# ========================================
# 🚀 DÉPLOIEMENT ET PRODUCTION
# ========================================

# URL de base en production
PRODUCTION_API_URL="https://api.pestalert.com"
PRODUCTION_WEB_URL="https://pestalert.com"
PRODUCTION_DASHBOARD_URL="https://admin.pestalert.com"

# Configuration SSL
SSL_CERT_PATH="/path/to/ssl/cert.pem"
SSL_KEY_PATH="/path/to/ssl/key.pem"

# ========================================
# 🧪 TESTS ET DÉVELOPPEMENT
# ========================================

# Base de données de test
TEST_DATABASE_URL="postgresql://username:password@localhost:5432/pestalert_test"

# Configuration des tests
TEST_TIMEOUT=30000
TEST_PARALLEL=true

# Mock services en développement
MOCK_OPENEPI=false
MOCK_WHATSAPP=false
MOCK_NOTIFICATIONS=false

# ========================================
# 📦 CONFIGURATION PAR PACKAGE
# ========================================

# Ces variables seront utilisées dans les .env de chaque package
# Vous pouvez les référencer avec ${VARIABLE_NAME} dans les .env locaux

# Core package
CORE_DATABASE_URL="${DATABASE_URL}"
CORE_JWT_SECRET="${JWT_SECRET}"

# API package  
API_DATABASE_URL="${DATABASE_URL}"
API_JWT_SECRET="${JWT_SECRET}"
API_PORT="${API_PORT}"

# Bot package
BOT_OPENEPI_CLIENT_ID="${OPENEPI_CLIENT_ID}"
BOT_OPENEPI_CLIENT_SECRET="${OPENEPI_CLIENT_SECRET}"
BOT_WHATSAPP_SESSION_PATH="${WHATSAPP_SESSION_PATH}"

# Dashboard package
DASHBOARD_API_URL="${API_BASE_URL}"
DASHBOARD_SOCKET_URL="${SOCKET_URL}"
DASHBOARD_MAP_API_KEY="${MAP_API_KEY}"

# Web package
WEB_API_URL="${API_BASE_URL}"

# ========================================
# 📝 NOTES IMPORTANTES
# ========================================

# 1. Changez TOUS les secrets en production
# 2. Utilisez des variables d'environnement sécurisées pour les secrets
# 3. Ne commitez JAMAIS ce fichier avec de vraies valeurs
# 4. Utilisez un gestionnaire de secrets en production (AWS Secrets Manager, etc.)
# 5. Documentez toute nouvelle variable ajoutée

# ========================================
# 🔗 LIENS UTILES
# ========================================

# Documentation OpenEPI: https://docs.openepi.io
# WhatsApp Web.js: https://github.com/pedroslopez/whatsapp-web.js
# Prisma: https://www.prisma.io/docs
# Neon PostgreSQL: https://neon.tech
