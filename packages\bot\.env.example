# Configuration du Bot WhatsApp PestAlert

# Chemin de stockage des sessions WhatsApp
WHATSAPP_SESSION_PATH=./sessions

# Configuration OpenEPI
OPENEPI_BASE_URL=https://api.openepi.io
OPENEPI_AUTH_URL=https://auth.openepi.io/realms/openepi/protocol/openid-connect/token
OPENEPI_TIMEOUT=30000
OPENEPI_CLIENT_ID=aresgn-testpestsAPI
OPENEPI_CLIENT_SECRET=gHrAAcKkMkvEDfDijdqqBXULbqjGzlyK

# Configuration WhatsApp
WHATSAPP_SESSION_SECRET=your_secret_key

# Configuration de logging
LOG_LEVEL=info

# Configuration optionnelle pour cache (Redis)
REDIS_URL=redis://localhost:6379

# Configuration base de données (optionnel)
DB_CONNECTION_STRING=your_database_url

# Webhook pour notifications d'alertes (optionnel)
ALERT_NOTIFICATION_WEBHOOK=your_webhook_url

# Configuration des notifications d'urgence
EMERGENCY_PHONE=+33123456789
EMERGENCY_EMAIL=<EMAIL>

# Configuration pour développement
NODE_ENV=development
DEBUG=true

# Numéros autorisés (optionnel, pour limiter l'accès)
# ALLOWED_NUMBERS=+33123456789,+33987654321
