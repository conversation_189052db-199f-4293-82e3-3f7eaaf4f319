{"name": "@pestalert/api", "version": "1.0.0", "main": "dist/server.js", "scripts": {"build": "tsc", "dev": "ts-node src/server.ts", "start": "node dist/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "axios": "^1.4.0", "node-cron": "^3.0.2", "socket.io": "^4.7.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-validator": "^7.0.0", "dotenv": "^16.0.0", "cookie-parser": "^1.4.6", "@pestalert/core": "file:../core"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.0", "@types/multer": "^1.4.7", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "ts-node": "^10.9.0", "nodemon": "^3.0.0", "typescript": "^5.0.0"}}