{"name": "@pestalert/core", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:init": "ts-node src/scripts/initDatabase.ts", "db:reset": "npx prisma migrate reset && npm run db:init"}, "dependencies": {"prisma": "^5.0.0", "@prisma/client": "^5.0.0", "zod": "^3.22.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.0", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.2"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "ts-node": "^10.9.0"}}