{"name": "@pestalert/bot", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "dev:simple": "ts-node src/bot-simple.ts", "start": "node dist/index.js", "test": "jest", "test:services": "ts-node src/test-services.ts", "test:auth": "ts-node src/test-auth.ts", "test:filters": "ts-node src/test-filters.ts", "explore:api": "ts-node src/explore-api.ts", "test:routes": "ts-node src/test-routes.ts", "test:new-flow": "ts-node src/test-new-flow.ts", "test:enhanced-health": "ts-node src/test-enhanced-health-analysis.ts"}, "dependencies": {"@types/form-data": "^2.2.1", "axios": "^1.4.0", "dotenv": "^16.0.0", "form-data": "^4.0.3", "node-schedule": "^2.1.1", "qrcode-terminal": "^0.12.0", "sharp": "^0.32.0", "whatsapp-web.js": "^1.23.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/qrcode-terminal": "^0.12.2", "ts-node": "^10.9.0"}}