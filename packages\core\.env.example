# Base de données PostgreSQL
# Pour le développement local, vous pouvez utiliser une base PostgreSQL locale
# Pour la production, utilisez Neon ou un autre service PostgreSQL cloud
DATABASE_URL="postgresql://username:password@localhost:5432/pestalert_dev"

# Exemple pour Neon (production)
# DATABASE_URL="postgresql://username:<EMAIL>/pestalert"

# Configuration du dashboard
DASHBOARD_INTEGRATION_ENABLED=true

# JWT pour l'authentification du dashboard
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# Configuration Redis (optionnel, pour le cache)
REDIS_URL=redis://localhost:6379

# Configuration des logs
LOG_LEVEL=info
LOG_RETENTION_DAYS=90

# Configuration des métriques
METRICS_COLLECTION_INTERVAL=300000  # 5 minutes en millisecondes
METRICS_RETENTION_DAYS=90
