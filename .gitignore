# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js build output
.next/

# Nuxt.js build / generate output
.nuxt/

# Gatsby files
.cache/
public/

# Vuepress build output
.vuepress/dist/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# WhatsApp sessions
packages/bot/sessions/
packages/bot/.wwebjs_auth/
packages/bot/.wwebjs_cache/

# Prisma
packages/core/prisma/migrations/
packages/core/prisma/dev.db*

# Build outputs per package
packages/*/dist/
packages/*/build/
packages/*/.next/
packages/*/.nuxt/
apps/*/dist/
apps/*/build/
apps/*/.next/
apps/*/.nuxt/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Temporary files
.tmp/
temp/
*.tmp

# Package manager lock files (keep only one)
# yarn.lock
# package-lock.json
# pnpm-lock.yaml

# Testing
.jest/
test-results/
playwright-report/
test-results.xml

# Storybook build outputs
storybook-static/

# Local development
.local/
.vercel/
.netlify/

# Database
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup
