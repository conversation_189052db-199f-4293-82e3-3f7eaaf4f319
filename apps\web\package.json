{"name": "@pestalert/web", "version": "1.0.0", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.0.0", "@tanstack/react-query": "^4.0.0", "react-hook-form": "^7.45.0", "tailwindcss": "^3.3.0", "lucide-react": "^0.263.0", "axios": "^1.4.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.0.0", "vite": "^4.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0"}}