{"name": "pestalert-bot", "version": "1.0.0", "private": true, "scripts": {"dev": "nx run-many --target=serve --projects=api,dashboard,bot --parallel", "build": "nx run-many --target=build --projects=api,dashboard,web", "test": "nx run-many --target=test --all", "db:migrate": "cd packages/core && npx prisma migrate dev", "db:generate": "cd packages/core && npx prisma generate", "db:studio": "cd packages/core && npx prisma studio", "db:reset": "cd packages/core && npx prisma migrate reset", "bot:start": "nx serve bot", "api:start": "nx serve api", "dashboard:start": "nx serve dashboard", "lint": "nx run-many --target=lint --all", "format": "nx run-many --target=format --all"}, "devDependencies": {"@nx/js": "^17.0.0", "@nx/node": "^17.0.0", "@nx/react": "^17.0.0", "@nx/workspace": "^17.0.0", "nx": "^17.0.0", "typescript": "^5.0.0"}}