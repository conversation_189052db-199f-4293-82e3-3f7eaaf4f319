{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@pestalert/core": ["packages/core/src"], "@pestalert/shared": ["packages/shared/src"], "@pestalert/api": ["packages/api/src"], "@pestalert/bot": ["packages/bot/src"], "@pestalert/dashboard": ["packages/dashboard/src"]}}, "include": ["packages/*/src/**/*"], "exclude": ["node_modules", "dist", "build"]}