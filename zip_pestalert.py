#!/usr/bin/env python3
"""
🗜️ Script Simple pour Zipper PestAlert avec les fichiers .env
===========================================================

Ce script crée une archive ZIP complète du projet PestAlert
en incluant les fichiers .env et en excluant seulement les
dossiers volumineux comme node_modules et dist.

Usage: python zip_pestalert.py
"""

import os
import zipfile
from datetime import datetime
from pathlib import Path

def should_exclude_directory(dir_name):
    """Détermine si un dossier doit être exclu"""
    excluded_dirs = {
        'node_modules',
        'dist',
        'build',
        '.git',
        '.vscode',
        '.idea',
        'logs',
        'sessions',
        'coverage',
        '.cache',
        '.tmp',
        'tmp',
        'temp'
    }
    return dir_name.lower() in excluded_dirs

def should_exclude_file(file_name):
    """Détermine si un fichier doit être exclu"""
    # Très peu de fichiers exclus - on garde presque tout
    excluded_files = {
        '.ds_store',
        'thumbs.db',
        'desktop.ini'
    }
    
    excluded_extensions = {
        '.log',
        '.tmp'
    }
    
    file_lower = file_name.lower()
    
    # Vérifier les fichiers spécifiques
    if file_lower in excluded_files:
        return True
    
    # Vérifier les extensions
    for ext in excluded_extensions:
        if file_lower.endswith(ext):
            return True
    
    return False

def create_archive():
    """Crée l'archive ZIP du projet"""
    # Nom du fichier avec timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_filename = f'pestalert-bot-{timestamp}.zip'
    
    print("🗜️  Création de l'archive PestAlert")
    print("="*50)
    print(f"📦 Nom de l'archive: {zip_filename}")
    print("✅ INCLUS: Tous les fichiers sources, .env, configuration")
    print("❌ EXCLUS: node_modules, dist, .git, logs, sessions")
    print("="*50)
    print()
    
    files_added = 0
    files_excluded = 0
    total_size = 0
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
            
            for root, dirs, files in os.walk('.'):
                root_path = Path(root)
                
                # Filtrer les dossiers à exclure
                original_dirs = dirs[:]
                dirs[:] = []
                for d in original_dirs:
                    if not should_exclude_directory(d):
                        dirs.append(d)
                    else:
                        print(f"📁❌ Dossier exclu: {root_path / d}")
                
                # Traiter les fichiers
                for file in files:
                    file_path = root_path / file
                    
                    if should_exclude_file(file):
                        files_excluded += 1
                        print(f"📄❌ Fichier exclu: {file_path}")
                    else:
                        try:
                            # Calculer la taille
                            file_size = file_path.stat().st_size
                            total_size += file_size
                            
                            # Ajouter le fichier à l'archive
                            arcname = str(file_path).lstrip('./')
                            arcname = arcname.replace('\\', '/')  # Normaliser les chemins
                            zipf.write(file_path, arcname)
                            
                            files_added += 1
                            
                            # Afficher seulement les fichiers importants pour ne pas spammer
                            if any(important in str(file_path).lower() for important in ['.env', 'package.json', 'readme', '.md', '.ts', '.js', '.py']):
                                size_str = f"({file_size:,} bytes)" if file_size > 1024 else f"({file_size} bytes)"
                                print(f"📄✅ {file_path} {size_str}")
                            
                        except Exception as e:
                            print(f"📄⚠️  Erreur avec {file_path}: {e}")
                            files_excluded += 1
        
        # Statistiques finales
        archive_size = os.path.getsize(zip_filename)
        compression_ratio = (1 - archive_size / total_size) * 100 if total_size > 0 else 0
        
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DE L'ARCHIVAGE")
        print("="*60)
        print(f"📦 Archive créée: {zip_filename}")
        print(f"✅ Fichiers inclus: {files_added:,}")
        print(f"❌ Fichiers exclus: {files_excluded:,}")
        print(f"📏 Taille originale: {total_size / (1024*1024):.2f} MB")
        print(f"🗜️  Taille compressée: {archive_size / (1024*1024):.2f} MB")
        print(f"📉 Compression: {compression_ratio:.1f}%")
        print("\n🎉 Archive créée avec succès!")
        print("\n💡 L'archive contient:")
        print("   ✅ Tous les fichiers .env")
        print("   ✅ Code source complet")
        print("   ✅ Configuration et documentation")
        print("   ❌ Pas de node_modules (à réinstaller avec npm install)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR lors de la création de l'archive: {e}")
        return False

def main():
    """Fonction principale"""
    print("🌾 PestAlert - Création d'Archive Complète")
    print()
    
    # Vérifier qu'on est dans le bon dossier
    if not os.path.exists('package.json'):
        print("⚠️  ATTENTION: package.json non trouvé!")
        print("   Assurez-vous d'être dans le dossier racine de pestalert-bot")
        print()
        response = input("Continuer quand même? (y/N): ")
        if response.lower() != 'y':
            print("❌ Opération annulée.")
            return
    
    # Créer l'archive
    success = create_archive()
    
    if success:
        print("\n🚀 Prochaines étapes après extraction:")
        print("   1. cd pestalert-bot")
        print("   2. npm install")
        print("   3. Configurer .env_global si nécessaire")
        print("   4. npm run setup")
        print("   5. start_all.bat")
    
    input("\nAppuyez sur Entrée pour fermer...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Opération annulée par l'utilisateur.")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        input("Appuyez sur Entrée pour fermer...")
