#!/usr/bin/env python3
"""
🗜️ Script de Création d'Archive PestAlert
==========================================

Ce script crée une archive ZIP du projet PestAlert en excluant
les dossiers volumineux et temporaires comme node_modules et dist.

Usage:
    python create_archive.py [options]

Options:
    --output, -o    Nom du fichier de sortie (défaut: pestalert-bot-YYYYMMDD.zip)
    --exclude, -e   Dossiers/fichiers supplémentaires à exclure
    --include-env   Inclure les fichiers .env (par défaut: exclus)
    --help, -h      Afficher cette aide

Exemples:
    python create_archive.py
    python create_archive.py -o pestalert-backup.zip
    python create_archive.py --exclude logs,temp --include-env
"""

import os
import zipfile
import argparse
import sys
from datetime import datetime
from pathlib import Path

# Configuration par défaut
DEFAULT_EXCLUDES = {
    # Dossiers de dépendances
    'node_modules',
    'dist',
    'build',
    '.next',
    
    # Dossiers de cache et temporaires
    '.cache',
    '.tmp',
    'tmp',
    'temp',
    '.temp',
    
    # Dossiers de logs
    'logs',
    'log',
    
    # Dossiers de sessions et données temporaires
    'sessions',
    '.sessions',
    
    # Dossiers Git et IDE
    '.git',
    '.vscode',
    '.idea',
    
    # Dossiers de test et coverage
    'coverage',
    '.nyc_output',
    'test-results',
    
    # Dossiers de déploiement
    'deploy',
    'deployment',
    
    # Autres dossiers temporaires
    '.DS_Store',
    'Thumbs.db'
}

DEFAULT_FILE_EXCLUDES = {
    # Fichiers de configuration sensibles
    '.env',
    '.env.local',
    '.env.production',
    '.env.development',
    '.env_global',
    
    # Fichiers de logs
    '*.log',
    '*.log.*',
    
    # Fichiers temporaires
    '*.tmp',
    '*.temp',
    '*.swp',
    '*.swo',
    
    # Fichiers de cache
    '*.cache',
    
    # Fichiers système
    '.DS_Store',
    'Thumbs.db',
    'desktop.ini',
    
    # Fichiers de lock (optionnel, gardés par défaut)
    # 'package-lock.json',
    # 'yarn.lock'
}

class PestAlertArchiver:
    def __init__(self, source_dir='.', output_file=None, custom_excludes=None, include_env=False):
        self.source_dir = Path(source_dir).resolve()
        self.include_env = include_env
        
        # Nom du fichier de sortie
        if output_file:
            self.output_file = output_file
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.output_file = f'pestalert-bot-{timestamp}.zip'
        
        # Configuration des exclusions
        self.excluded_dirs = DEFAULT_EXCLUDES.copy()
        self.excluded_files = DEFAULT_FILE_EXCLUDES.copy()
        
        if not include_env:
            # Garder les exclusions de fichiers .env
            pass
        else:
            # Retirer les fichiers .env des exclusions
            env_files = {f for f in self.excluded_files if f.startswith('.env')}
            self.excluded_files -= env_files
        
        if custom_excludes:
            self.excluded_dirs.update(custom_excludes)
        
        # Statistiques
        self.stats = {
            'total_files': 0,
            'included_files': 0,
            'excluded_files': 0,
            'total_size': 0,
            'compressed_size': 0
        }

    def should_exclude_dir(self, dir_path):
        """Vérifie si un dossier doit être exclu"""
        dir_name = dir_path.name
        return dir_name in self.excluded_dirs

    def should_exclude_file(self, file_path):
        """Vérifie si un fichier doit être exclu"""
        file_name = file_path.name
        
        # Vérification exacte
        if file_name in self.excluded_files:
            return True
        
        # Vérification avec wildcards
        for pattern in self.excluded_files:
            if '*' in pattern:
                if pattern.startswith('*') and file_name.endswith(pattern[1:]):
                    return True
                elif pattern.endswith('*') and file_name.startswith(pattern[:-1]):
                    return True
        
        return False

    def get_relative_path(self, file_path):
        """Obtient le chemin relatif depuis la racine du projet"""
        try:
            return file_path.relative_to(self.source_dir)
        except ValueError:
            return file_path

    def create_archive(self):
        """Crée l'archive ZIP"""
        print(f"🗜️  Création de l'archive PestAlert...")
        print(f"📁 Source: {self.source_dir}")
        print(f"📦 Archive: {self.output_file}")
        print(f"🔒 Inclure .env: {'Oui' if self.include_env else 'Non'}")
        print()

        try:
            with zipfile.ZipFile(self.output_file, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                self._add_directory_to_zip(zipf, self.source_dir)
            
            # Calculer la taille compressée
            self.stats['compressed_size'] = os.path.getsize(self.output_file)
            
            self._print_summary()
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la création de l'archive: {e}")
            return False

    def _add_directory_to_zip(self, zipf, dir_path, base_path=None):
        """Ajoute récursivement un dossier à l'archive"""
        if base_path is None:
            base_path = dir_path
        
        try:
            for item in dir_path.iterdir():
                if item.is_dir():
                    if not self.should_exclude_dir(item):
                        print(f"📁 Ajout du dossier: {self.get_relative_path(item)}")
                        self._add_directory_to_zip(zipf, item, base_path)
                    else:
                        print(f"⏭️  Exclusion du dossier: {self.get_relative_path(item)}")
                        
                elif item.is_file():
                    self.stats['total_files'] += 1
                    file_size = item.stat().st_size
                    self.stats['total_size'] += file_size
                    
                    if not self.should_exclude_file(item):
                        # Calculer le chemin dans l'archive
                        arcname = self.get_relative_path(item)
                        zipf.write(item, arcname)
                        
                        self.stats['included_files'] += 1
                        print(f"✅ Ajout: {arcname} ({self._format_size(file_size)})")
                    else:
                        self.stats['excluded_files'] += 1
                        print(f"⏭️  Exclusion: {self.get_relative_path(item)}")
                        
        except PermissionError as e:
            print(f"⚠️  Accès refusé: {dir_path} - {e}")
        except Exception as e:
            print(f"❌ Erreur avec {dir_path}: {e}")

    def _format_size(self, size_bytes):
        """Formate la taille en unités lisibles"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"

    def _print_summary(self):
        """Affiche le résumé de l'archivage"""
        print("\n" + "="*50)
        print("📊 RÉSUMÉ DE L'ARCHIVAGE")
        print("="*50)
        print(f"📁 Dossier source: {self.source_dir}")
        print(f"📦 Archive créée: {self.output_file}")
        print()
        print(f"📄 Fichiers totaux: {self.stats['total_files']}")
        print(f"✅ Fichiers inclus: {self.stats['included_files']}")
        print(f"⏭️  Fichiers exclus: {self.stats['excluded_files']}")
        print()
        print(f"📏 Taille originale: {self._format_size(self.stats['total_size'])}")
        print(f"🗜️  Taille compressée: {self._format_size(self.stats['compressed_size'])}")
        
        if self.stats['total_size'] > 0:
            compression_ratio = (1 - self.stats['compressed_size'] / self.stats['total_size']) * 100
            print(f"📉 Taux de compression: {compression_ratio:.1f}%")
        
        print("\n🎉 Archive créée avec succès!")

def main():
    parser = argparse.ArgumentParser(
        description="Crée une archive ZIP du projet PestAlert",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples:
  python create_archive.py
  python create_archive.py -o pestalert-backup.zip
  python create_archive.py --exclude logs,temp --include-env
        """
    )
    
    parser.add_argument(
        '--output', '-o',
        help='Nom du fichier de sortie (défaut: pestalert-bot-YYYYMMDD.zip)'
    )
    
    parser.add_argument(
        '--exclude', '-e',
        help='Dossiers/fichiers supplémentaires à exclure (séparés par des virgules)'
    )
    
    parser.add_argument(
        '--include-env',
        action='store_true',
        help='Inclure les fichiers .env (par défaut: exclus pour la sécurité)'
    )
    
    parser.add_argument(
        '--source', '-s',
        default='.',
        help='Dossier source à archiver (défaut: dossier courant)'
    )
    
    args = parser.parse_args()
    
    # Traiter les exclusions supplémentaires
    custom_excludes = set()
    if args.exclude:
        custom_excludes = set(item.strip() for item in args.exclude.split(','))
    
    # Vérifier que le dossier source existe
    source_path = Path(args.source)
    if not source_path.exists():
        print(f"❌ Erreur: Le dossier source '{source_path}' n'existe pas.")
        sys.exit(1)
    
    if not source_path.is_dir():
        print(f"❌ Erreur: '{source_path}' n'est pas un dossier.")
        sys.exit(1)
    
    # Créer l'archiveur et lancer l'archivage
    archiver = PestAlertArchiver(
        source_dir=args.source,
        output_file=args.output,
        custom_excludes=custom_excludes,
        include_env=args.include_env
    )
    
    success = archiver.create_archive()
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
